Ansible playbooks allowing to maintain arb bots easier.

Right now we have only one [playbook](upgrade-bots.yaml) to upgrade bots.

# How to

## Upgrade my bots

1. Make an inventory file within [inventory](inventory) directory with your nickname, surname or something else
allowing to identify you. You could take as an example [inventory/ivanov.yaml](inventory/ivanov.yaml). There are some
description and links to official documentation how to make an inventory file.
2. Install `ansible` to your host machine:
   1. For macOS users `brew install ansible`
3. Run command `ansible-playbook -i inventory/<YOUR_INVENTORY_FILE> upgrade-bots.yaml`

If you want to upgrade a specific bot you can pass extra argument `--limit` and pass the name of your bot. For example,
we have next inventory file

```yaml
# inventory/mybots.yaml
bots:
  hosts:
    mybot1:
      ansible_host: *******
    mybot2:
      ansible_host: *******
```

```bash
ansible-playbook -i inventory/mybots.yaml upgrade-bots.yaml --limit mybot1
```

The command above upgrades only mybot1.

```bash
ansible-playbook -i inventory/mybots.yaml upgrade-bots.yaml
```

This command upgrades all bots from inventory file `inventory/mybots.yaml`.

```bash
ansible-playbook -i inventory/mybots.yaml upgrade-bots.yaml -e bot_version=ai-sandbox --limit mybot2
```

Installing bot version `ai-sandbox` on a server with bot `mybot2`.

## Prepare a server to run trading bot

1. Prepare your Telegram ID
2. Prepare Telegram bot token
3. Add your new server to your inventory file
4. Run the playbook [environment-bot.yaml](environment-bot.yaml) like:

   ```bash
   # In interactive mode where you will be asked to enter your telegram ID and telegram key for bot
   ansible-playbook -i inventory/my-inventory.yaml environment-bot.yaml --limit my-new-bot --ask-vault-pass

   # Non-interactive move
   ansible-playbook -i inventory/my-inventory.yaml environment-bot.yaml --limit my-new-bot --ask-vault-pass -e "telegram_key=<TG_BOT_KEY> telegram_id=<YOUR_TG_ID>"
   ```
5. Ansible asks the password to decrypt a secret file placed at [secrets/docker/shared.yaml](secrets/docker/shared.yaml).
You should ask this password for Ivanov and keep it in secret
6. After that you need to add exchanges keys into `~/.env` file and to run `~/restart.sh` script.

## Upgrade server software

To install releases just run the playbook [upgrade-system.yaml](upgrade-system.yaml).

```shell
# Install software upgrades for all bot servers
ansible-playbook -i inventory/<YOUR_INVENTORY> upgrade-system.yaml

# Install software upgrades for a specific bot server
ansible-playbook -i inventory/<YOUR_INVENTORY> upgrade-system.yaml --limit <SRV_NAME>
```

This playbook correct handles installing kernel upgrades and docker's. Before installing docker upgrades
it stops all trading as well as before restarting a server to apply kernel patches.

## Pause bots
To pause bots run the playbook [pause-bots.yaml](pause-bots.yaml)

```shell
# Pause all bots
ansible-playbook -i inventory/<YOUR_INVENTORY> pause-bots.yaml

# Pause a specific bot server
ansible-playbook -i inventory/<YOUR_INVENTORY> pause-bots.yaml --limit <SRV_NAME>
```

## Migrate a bot from one server to another

### Preparation steps

Before running the migration playbook, you need to:

1. Prepare a destination server following the steps in [Prepare a server to run trading bot](#prepare-a-server-to-run-trading-bot)
   - Note: When running the `environment-bot.yaml` playbook for a destination server in a migration, you can skip setting the Telegram ID and bot version as these will be migrated from the source server
2. Add the IP address of the destination server to your exchange's whitelist to ensure the new server can open/close positions and perform other trading operations
3. Verify that the destination server has all necessary permissions and access

Once the destination server is properly prepared, you can proceed with the migration.

### Migration

To migrate a trading bot from one server to another, use the playbook [migrate-trading-bots.yaml](migrate-trading-bots.yaml). This playbook will:

1. Pause trading on the source server
2. Stop Docker containers on the source server
3. Securely transfer configuration files and data to the destination server
4. Start the bot on the destination server
5. Verify the bot is running properly

To run the migration:

```shell
# Run the migration playbook
ansible-playbook migrate-trading-bots.yaml
```

The playbook will prompt you for:
- Source host (hostname or IP) to migrate FROM
- Destination host (hostname or IP) to migrate TO
- Confirmation to proceed with migration

Example migration session:

```
$ ansible-playbook migrate-trading-bots.yaml

Enter the source host (hostname or IP) to migrate FROM: ************
Enter the destination host (hostname or IP) to migrate TO: ************
Are you sure you want to migrate? (yes/no) [no]: yes
```

After migration completes successfully, verify that your bot is running properly on the destination server before decommissioning the source server.
